<?php
#region region DOCS
/** @var Hit $newhit */
/** @var Hit[] $hits */
/** @var Hit|null $current_active_hit */

/** @var string $trk_id */

use App\classes\Hit;

#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title id="pageTitle">
		<?php
		if ($current_active_hit) {
			// Calculate elapsed time for hourly rate
			$start_time      = new DateTime($current_active_hit->getFecha());
			$current_time    = new DateTime();
			$diff            = $current_time->diff($start_time);
			$elapsed_minutes = ($diff->h * 60) + $diff->i;
			
			// Calculate hourly rate (avoid division by zero)
			$hourly_rate = 0;
			if ($elapsed_minutes > 0) {
				$hourly_rate = ($current_active_hit->getPago() / $elapsed_minutes) * 60;
			}
			
			// Format active time as HH:MM
			$active_time = sprintf('%02d:%02d', $diff->h, $diff->i);
			
			// Dynamic title with hourly rate and active time
			echo 'My Dash | $' . number_format($hourly_rate, 2) . '/hr - ' . $active_time . ' activo';
		} else {
			echo 'My Dash | Hits';
		}
		?>
	</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region region HEAD ?>
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
	<?php #endregion head ?>
	
	<style>
        #trk-id-display {
            font-size: 1em;
            background-color: #6c757d !important;
            color: white !important;
        }

        /* Active Hit Panel Animations */
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.7;
            }
            50% {
                transform: scale(1.05);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0.7;
            }
        }

        @keyframes slideInUp {
            from {
                transform: translateY(30px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        #activeHitPanel {
            animation: slideInUp 0.5s ease-out;
        }

        .font-monospace {
            font-family: 'Courier New', Courier, monospace;
        }
	</style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/topbar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<!-- BEGIN page-header -->
		<h1 class="page-header">Hits</h1>
		
		<hr>
		<!-- END page-header -->
		
		<?php #region region Main Form Structure (for table and hidden inputs) ?>
		<form action="listado-hits" method="POST" id="mainHitsForm">
			<input type="hidden" id="selidhit" name="selidhit">
			
			<?php #region region SUBMIT sub_edithit ?>
			<div class="col" style="display: none">
				<button type="submit" form="mainHitsForm" id="sub_edithit" name="sub_edithit" class="btn btn-success w-100">
					sub_edithit
				</button>
			</div>
			<?php #endregion sub_edithit ?>
			
			<!-- Earnings Display -->
			<div class="row mt-3 mb-3">
				<!-- Today's Earnings -->
				<div class="col-md-4">
					<div class="card bg-gradient-success text-white shadow-lg border-0">
						<div class="card-body text-center py-4">
							<div class="d-flex align-items-center justify-content-center mb-2">
								<i class="fa fa-dollar-sign fa-2x me-3"></i>
								<div>
									<h6 class="card-subtitle mb-1 text-white-500">Ganancias de Hoy</h6>
									<h2 class="card-title mb-0 fw-bold">
										$<?php echo format_currency_usd($todays_earnings ?? 0); ?>
									</h2>
								</div>
							</div>
							<small class="text-white-75">
								<i class="fa fa-check-circle me-1"></i>
								Solo hits completados y no retornados
							</small>
						</div>
					</div>
				</div>
				
				<!-- Weekly Earnings -->
				<div class="col-md-4">
					<div class="card bg-gradient-info text-white shadow-lg border-0">
						<div class="card-body text-center py-4">
							<div class="d-flex align-items-center justify-content-center mb-2">
								<i class="fa fa-calendar-week fa-2x me-3"></i>
								<div>
									<h6 class="card-subtitle mb-1 text-white-500">Ganancias de la Semana</h6>
									<h2 class="card-title mb-0 fw-bold">
										$<?php echo format_currency_usd($weekly_earnings ?? 0); ?>
									</h2>
								</div>
							</div>
							<small class="text-white-75">
								<i class="fa fa-check-circle me-1"></i>
								Solo hits completados y no retornados
							</small>
						</div>
					</div>
				</div>
				
				<!-- Monthly Earnings -->
				<div class="col-md-4">
					<div class="card bg-gradient-warning text-white shadow-lg border-0">
						<div class="card-body text-center py-4">
							<div class="d-flex align-items-center justify-content-center mb-2">
								<i class="fa fa-calendar fa-2x me-3"></i>
								<div>
									<h6 class="card-subtitle mb-1 text-white-500">Ganancias del Mes</h6>
									<h2 class="card-title mb-0 fw-bold">
										$<?php echo format_currency_usd($monthly_earnings ?? 0); ?>
									</h2>
								</div>
							</div>
							<small class="text-white-75">
								<i class="fa fa-check-circle me-1"></i>
								Solo hits completados y no retornados
							</small>
						</div>
					</div>
				</div>
			</div>
			
			<!-- Active Hit Panel -->
			<div class="row mt-3 mb-3" id="activeHitPanel" style="display: none;">
				<div class="col-12">
					<div class="card bg-gradient-danger text-white shadow-lg border-0 position-relative overflow-hidden">
						<!-- Animated background elements -->
						<div class="position-absolute top-0 start-0 w-100 h-100 opacity-10">
							<div class="position-absolute" style="top: -20px; right: -20px; width: 100px; height: 100px; background: rgba(255,255,255,0.1); border-radius: 50%; animation: pulse 2s infinite;"></div>
							<div class="position-absolute" style="bottom: -30px; left: -30px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%; animation: pulse 3s infinite;"></div>
						</div>
						
						<div class="card-body text-center py-4 position-relative">
							<div class="row align-items-center">
								<!-- Tarifa por Hora (moved to first position) -->
								<div class="col-md-3">
									<div class="text-center">
										<h6 class="card-subtitle mb-1 text-white-75">Tarifa por Hora</h6>
										<h2 class="card-title mb-0 fw-bold text-white-500" id="activeHourlyRate">
											$0.00/hr
										</h2>
										<small class="text-white-50">
											<i class="fa fa-trending-up me-1"></i>
											En tiempo real
										</small>
									</div>
								</div>
								
								<!-- Hit Information (middle section with vertical centering) -->
								<div class="col-md-6 d-flex align-items-center justify-content-center">
									<div class="text-start w-100">
										<div class="d-flex flex-column justify-content-center h-100">
											<div class="mb-1">
												<i class="fa fa-play-circle fa-lg text-success w-20px"></i>
												<span id="activeHitDescription">-</span>
											</div>
											<div class="mb-1">
												<i class="fa fa-user fa-lg text-info w-20px"></i>
												<strong></strong> <span id="activeHitRequester">-</span>
											</div>
											<div class="mb-1">
												<i class="fa fa-comment-dollar fa-lg text-warning w-20px"></i>
												<strong></strong> $<span id="activeHitPago">0.00</span>
											</div>
										</div>
									</div>
								</div>
								
								<!-- Tiempo Activo (moved to last position) -->
								<div class="col-md-3">
									<div class="d-flex align-items-center justify-content-center mb-2">
										<i class="fa fa-clock fa-3x me-3 text-white-500" style="animation: pulse 1.5s infinite;"></i>
										<div>
											<h6 class="card-subtitle mb-1 text-white-75">Tiempo Activo</h6>
											<h1 class="card-title mb-0 fw-bold font-monospace" id="activeDuration">
												00:00
											</h1>
										</div>
									</div>
								</div>
							</div>
							
							<div class="mt-3">
								<small class="text-white-75">
									<i class="fa fa-info-circle me-1"></i>
									Hit en progreso - Actualización automática cada segundo
								</small>
							</div>
							
							<!-- Terminar Hit Button -->
							<div class="mt-3">
								<button type="button"
								        class="btn btn-success btn-lg w-100 fw-bold"
								        id="finishActiveHitBtn"
								        onclick="finishActiveHit()"
								        style="font-size: 1.1rem; padding: 12px 0; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
									<i class="fa fa-check-circle fa-lg me-2"></i>
									TERMINAR HIT
								</button>
							</div>
							
							<!-- Retornar Hit Button -->
							<div class="mt-3">
								<button type="button"
								        class="btn btn-warning btn-lg w-100 fw-bold"
								        id="returnActiveHitBtn"
								        onclick="returnActiveHit()"
								        style="font-size: 1.1rem; padding: 12px 0; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
									<i class="fa fa-undo fa-lg me-2"></i>
									RETORNAR HIT
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<!-- Button to trigger Add Hit modal -->
			<div class="row mt-3 mb-3">
				<div class="col-md-12">
					<button type="button" class="btn btn-primary w-100" data-bs-toggle="modal" data-bs-target="#addHitModal">
						<i class="fa fa-plus me-1"></i> Nuevo Hit
					</button>
				</div>
			</div>
			
			<?php #region region PANEL hits ?>
			<div class="panel panel-inverse mt-3">
				<div class="panel-heading">
					<h4 class="panel-title d-flex justify-content-between align-items-center">
						<span>Hits de Hoy 
							<span id="trk-id-display"
							      class="badge bg-secondary ms-2 cursor-pointer"
							      onclick="copyTrkId()"
							      title="Click para copiar">
								<?php echo htmlspecialchars($trk_id); ?>
							</span>
						</span>
					</h4>
				</div>
				<!-- BEGIN panel-body -->
				<div class="p-1 table-nowrap" style="overflow: auto">
					<?php #region region TABLE hits ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th class="w-100px"></th>
							<th class="text-center">Descripción</th>
							<th class="text-center">Requester</th>
							<th class="text-center">Pago</th>
							<th class="text-center">Fecha</th>
							<th class="text-center">Duración</th>
							<th class="text-center">Estado</th>
							<th class="text-center">Retornado</th>
						</tr>
						</thead>
						<tbody class="fs-13px">
						<?php #region region ARRAY hits ?>
						<?php foreach ($hits as $hit): ?>
							<tr>
								<td>
									<i class="fa fa-pencil fa-md cursor-pointer me-1"
									   onclick="editHitModal('<?php echo limpiar_datos($hit->getId()); ?>');"
									   data-bs-toggle="tooltip"
									   title="Editar Hit"></i>
									<i class="fa fa-trash fa-md cursor-pointer text-danger me-1"
									   data-bs-toggle="modal"
									   data-bs-target="#mdl_delhit"
									   data-idhit="<?php echo limpiar_datos($hit->getId()) ?>"
									   title="Eliminar Hit"></i>
									<?php if ($hit->getTerminado() == 0): ?>
										<i class="fa fa-check fa-md cursor-pointer text-success me-1"
										   onclick="finishHit('<?php echo limpiar_datos($hit->getId()); ?>');"
										   data-bs-toggle="tooltip"
										   title="Terminar Hit"></i>
									<?php endif; ?>
									<?php if ($hit->getRetornado() == 0): ?>
										<i class="fa fa-undo fa-md cursor-pointer text-warning"
										   onclick="returnHit('<?php echo limpiar_datos($hit->getId()); ?>');"
										   data-bs-toggle="tooltip"
										   title="Retornar Hit"></i>
									<?php endif; ?>
								</td>
								<td>
									<?php echo $hit->getDescripcion(); ?>
									<?php if (!empty($hit->getNota())): ?>
										<i class="fa fa-info-circle ms-1 cursor-pointer" title="<?php echo htmlspecialchars($hit->getNota()); ?>"></i>
									<?php endif; ?>
								</td>
								<td class="text-center">
									<?php echo $hit->getRequester(); ?>
								</td>
								<td class="text-end">
									$<?php echo format_currency_usd($hit->getPago()); ?>
								</td>
								<td class="text-center">
									<?php echo date('Y-m-d H:i', strtotime($hit->getFecha())); ?>
								</td>
								<td class="text-center">
									<?php
									if ($hit->getFechaTerminado()) {
										$inicio   = new DateTime($hit->getFecha());
										$fin      = new DateTime($hit->getFechaTerminado());
										$duracion = $inicio->diff($fin);
										$minutos  = ($duracion->h * 60) + $duracion->i;
										echo $minutos . ' min';
									} else {
										echo '-';
									}
									?>
								</td>
								<td class="text-center">
									<?php if ($hit->getTerminado() == 0): ?>
										<span class="badge bg-warning">En progreso</span>
									<?php else: ?>
										<span class="badge bg-success">Terminado</span>
									<?php endif; ?>
								</td>
								<td class="text-center">
									<?php if ($hit->getRetornado() == 0): ?>
										<span class="badge bg-success">No</span>
									<?php else: ?>
										<span class="badge bg-danger">Sí</span>
									<?php endif; ?>
								</td>
							</tr>
						<?php endforeach; ?>
						<?php #endregion array hits ?>
						</tbody>
					</table>
					<?php #endregion table hits ?>
				</div>
				<!-- END panel-body -->
			</div>
			<?php #endregion panel hits ?>
			
			<?php #region region MODAL mdl_delhit ?>
			<div class="modal fade" id="mdl_delhit">
				<div class="modal-dialog modal-dialog-centered">
					<div class="modal-content">
						<input type="hidden" id="mdl_delhit_idhit" name="mdl_delhit_idhit">
						<div class="modal-header">
							<h4 class="modal-title">Eliminar Hit</h4>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<p>¿Está seguro de que desea eliminar este hit?</p>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" name="sub_delhit" class="btn btn-danger">Eliminar</button>
						</div>
					</div>
				</div>
			</div>
			<?php #endregion mdl_delhit ?>
		
		</form>
		<?php #endregion Main Form Structure ?>
		
		<!-- Add Hit Modal -->
		<div class="modal fade" id="addHitModal" tabindex="-1" aria-labelledby="addHitModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-lg">
				<div class="modal-content">
					<form action="listado-hits" method="POST" id="addHitFormInModal">
						<div class="modal-header">
							<h5 class="modal-title" id="addHitModalLabel">Agregar Nuevo Hit</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<!-- Descripcion -->
							<div class="mb-3">
								<label for="modal_descripcion" class="form-label">Descripción: <span class="text-danger">*</span></label>
								<input type="text" class="form-control" id="modal_descripcion" name="descripcion" required>
							</div>
							
							<!-- Requester -->
							<div class="mb-3">
								<label for="modal_requester" class="form-label">Requester: <span class="text-danger">*</span></label>
								<input type="text" class="form-control" id="modal_requester" name="requester" required>
							</div>
							
							<!-- Pago -->
							<div class="mb-3">
								<label for="modal_pago" class="form-label">Pago (USD): <span class="text-danger">*</span></label>
								<input type="number" step="0.01" min="0" class="form-control" id="modal_pago" name="pago" placeholder="0.00" required>
							</div>
							
							<!-- Nota -->
							<div class="mb-3">
								<label for="modal_nota" class="form-label">Nota:</label>
								<textarea class="form-control" id="modal_nota" name="nota" rows="3"></textarea>
							</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" name="sub_add" class="btn btn-success">Agregar Hit</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		
		<!-- Edit Hit Modal -->
		<div class="modal fade" id="editHitModal" tabindex="-1" aria-labelledby="editHitModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-lg">
				<div class="modal-content">
					<form action="listado-hits" method="POST" id="editHitFormInModal">
						<input type="hidden" id="edit_hit_id" name="edit_hit_id">
						<div class="modal-header">
							<h5 class="modal-title" id="editHitModalLabel">Editar Hit</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<!-- Descripcion -->
							<div class="mb-3">
								<label for="edit_descripcion" class="form-label">Descripción: <span class="text-danger">*</span></label>
								<input type="text" class="form-control" id="edit_descripcion" name="edit_descripcion" required>
							</div>
							
							<!-- Requester -->
							<div class="mb-3">
								<label for="edit_requester" class="form-label">Requester: <span class="text-danger">*</span></label>
								<input type="text" class="form-control" id="edit_requester" name="edit_requester" required>
							</div>
							
							<!-- Pago -->
							<div class="mb-3">
								<label for="edit_pago" class="form-label">Pago (USD): <span class="text-danger">*</span></label>
								<input type="number" step="0.01" min="0" class="form-control" id="edit_pago" name="edit_pago" placeholder="0.00" required>
							</div>
							
							<!-- Fecha de Inicio -->
							<div class="mb-3">
								<label class="form-label">Fecha de Inicio: <span class="text-danger">*</span></label>
								<div class="row">
									<div class="col-md-6">
										<label for="edit_fecha_date" class="form-label small">Fecha:</label>
										<input type="text" class="form-control datepicker" id="edit_fecha_date" name="edit_fecha_date" placeholder="YYYY-MM-DD" required>
									</div>
									<div class="col-md-6">
										<label for="edit_fecha_time" class="form-label small">Hora:</label>
										<input type="text" class="form-control timepicker" id="edit_fecha_time" name="edit_fecha_time" placeholder="HH:MM" required>
									</div>
								</div>
							</div>
							
							<!-- Fecha de Terminación (conditional) -->
							<div class="mb-3" id="edit_fecha_terminado_container" style="display: none;">
								<label class="form-label">Fecha de Terminación:</label>
								<div class="row">
									<div class="col-md-6">
										<label for="edit_fecha_terminado_date" class="form-label small">Fecha:</label>
										<input type="text" class="form-control datepicker" id="edit_fecha_terminado_date" name="edit_fecha_terminado_date" placeholder="YYYY-MM-DD">
									</div>
									<div class="col-md-6">
										<label for="edit_fecha_terminado_time" class="form-label small">Hora:</label>
										<input type="text" class="form-control timepicker" id="edit_fecha_terminado_time" name="edit_fecha_terminado_time" placeholder="HH:MM">
									</div>
								</div>
							</div>
							
							<!-- Nota -->
							<div class="mb-3">
								<label for="edit_nota" class="form-label">Nota:</label>
								<textarea class="form-control" id="edit_nota" name="edit_nota" rows="3"></textarea>
							</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" name="sub_update_hit" class="btn btn-success">Actualizar Hit</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		
		<!-- Finish Hit Confirmation Modal -->
		<div class="modal fade" id="finishHitModal" tabindex="-1" aria-labelledby="finishHitModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="finishHitModalLabel">Confirmar Terminación</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						¿Está seguro de que desea marcar este hit como terminado?
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="button" class="btn btn-success" id="confirmFinishHit">Sí, Terminar</button>
					</div>
				</div>
			</div>
		</div>
		
		<!-- Return Hit Confirmation Modal -->
		<div class="modal fade" id="returnHitModal" tabindex="-1" aria-labelledby="returnHitModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="returnHitModalLabel">Confirmar Retorno</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						¿Está seguro de que desea marcar este hit como retornado?
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="button" class="btn btn-warning" id="confirmReturnHit">Sí, Retornar</button>
					</div>
				</div>
			</div>
		</div>
	
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>

<!-- Date/Time Picker Scripts -->
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/bootstrap-timepicker/js/bootstrap-timepicker.min.js"></script>

<script type="text/javascript">
    // Initialize date and time pickers
    $(document).ready(function () {
        // Initialize datepickers
        $('.datepicker').datepicker({
            format        : "yyyy-mm-dd",
            todayHighlight: true,
            autoclose     : true,
            orientation   : "bottom"
        });
        
        // Initialize timepickers
        $('.timepicker').timepicker({
            showMeridian: false,  // 24-hour format
            defaultTime : false,   // Keeps the input empty until the user selects a time
            minuteStep  : 1         // Step for minutes selection
        });
    });
    
    // Form validation
    document.getElementById('addHitFormInModal').addEventListener('submit', function (e) {
        const descripcion = document.getElementById('modal_descripcion').value.trim();
        const requester   = document.getElementById('modal_requester').value.trim();
        const pago        = document.getElementById('modal_pago').value.trim();
        
        if (!descripcion) {
            e.preventDefault();
            alert('La descripción es requerida.');
            document.getElementById('modal_descripcion').focus();
            return false;
        }
        
        if (!requester) {
            e.preventDefault();
            alert('El requester es requerido.');
            document.getElementById('modal_requester').focus();
            return false;
        }
        
        if (!pago || parseFloat(pago) <= 0) {
            e.preventDefault();
            alert('El pago debe ser mayor a 0.');
            document.getElementById('modal_pago').focus();
            return false;
        }
    });
    
    // Auto-focus on modal open
    document.getElementById('addHitModal').addEventListener('shown.bs.modal', function () {
        document.getElementById('modal_descripcion').focus();
    });
    
    // Edit Hit Form validation
    document.getElementById('editHitFormInModal').addEventListener('submit', function (e) {
        const descripcion = document.getElementById('edit_descripcion').value.trim();
        const requester   = document.getElementById('edit_requester').value.trim();
        const pago        = document.getElementById('edit_pago').value.trim();
        const fechaDate   = document.getElementById('edit_fecha_date').value.trim();
        const fechaTime   = document.getElementById('edit_fecha_time').value.trim();
        
        if (!descripcion) {
            e.preventDefault();
            alert('La descripción es requerida.');
            document.getElementById('edit_descripcion').focus();
            return false;
        }
        
        if (!requester) {
            e.preventDefault();
            alert('El requester es requerido.');
            document.getElementById('edit_requester').focus();
            return false;
        }
        
        if (!pago || parseFloat(pago) <= 0) {
            e.preventDefault();
            alert('El pago debe ser mayor a 0.');
            document.getElementById('edit_pago').focus();
            return false;
        }
        
        if (!fechaDate) {
            e.preventDefault();
            alert('La fecha de inicio es requerida.');
            document.getElementById('edit_fecha_date').focus();
            return false;
        }
        
        if (!fechaTime) {
            e.preventDefault();
            alert('La hora de inicio es requerida.');
            document.getElementById('edit_fecha_time').focus();
            return false;
        }
        
        // Validate fecha_terminado fields if they are visible and required
        if ($('#edit_fecha_terminado_container').is(':visible')) {
            const fechaTerminadoDate = document.getElementById('edit_fecha_terminado_date').value.trim();
            const fechaTerminadoTime = document.getElementById('edit_fecha_terminado_time').value.trim();
            
            if (!fechaTerminadoDate) {
                e.preventDefault();
                alert('La fecha de terminación es requerida para hits completados.');
                document.getElementById('edit_fecha_terminado_date').focus();
                return false;
            }
            
            if (!fechaTerminadoTime) {
                e.preventDefault();
                alert('La hora de terminación es requerida para hits completados.');
                document.getElementById('edit_fecha_terminado_time').focus();
                return false;
            }
            
            // Validate that fecha_terminado is after fecha
            const fechaInicio      = new Date(fechaDate + ' ' + fechaTime);
            const fechaTerminacion = new Date(fechaTerminadoDate + ' ' + fechaTerminadoTime);
            
            if (fechaTerminacion <= fechaInicio) {
                e.preventDefault();
                alert('La fecha de terminación debe ser posterior a la fecha de inicio.');
                document.getElementById('edit_fecha_terminado_date').focus();
                return false;
            }
        }
    });
</script>

<script type="text/javascript">
    $('#mdl_delhit').on('shown.bs.modal', function (event) {
        const button          = $(event.relatedTarget);
        const recipient_idhit = button.data('idhit');
        
        const mdl_delhit_idhit = document.getElementById('mdl_delhit_idhit');
        
        mdl_delhit_idhit.value = recipient_idhit;
    })
</script>

<script type="text/javascript">
    function edithit($idhit) {
        const selidhit = document.getElementById('selidhit');
        selidhit.value = $idhit;
        
        document.getElementById('sub_edithit').click();
    }
</script>

<script type="text/javascript">
    // Initialize tooltips
    $(document).ready(function () {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList        = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
    
    // Variables to store hit ID for confirmation modals
    let currentHitIdForFinish = null;
    let currentHitIdForReturn = null;
    
    // Edit Hit Modal Function
    function editHitModal(hitId) {
        // AJAX call to get hit data
        $.ajax({
            url     : 'listado-hits',
            type    : 'POST',
            data    : {
                ajax_get_hit: true,
                hit_id      : hitId
            },
            dataType: 'json',
            success : function (response) {
                if (response.success) {
                    // Populate the edit modal with hit data
                    $('#edit_hit_id').val(response.data.id);
                    $('#edit_descripcion').val(response.data.descripcion);
                    $('#edit_requester').val(response.data.requester);
                    $('#edit_pago').val(response.data.pago);
                    $('#edit_nota').val(response.data.nota);
                    
                    // Populate fecha (start date/time) fields
                    $('#edit_fecha_date').val(response.data.fecha_date);
                    $('#edit_fecha_time').val(response.data.fecha_time);
                    
                    // Populate fecha_terminado fields and handle conditional display
                    $('#edit_fecha_terminado_date').val(response.data.fecha_terminado_date);
                    $('#edit_fecha_terminado_time').val(response.data.fecha_terminado_time);
                    
                    // Show/hide fecha_terminado fields based on hit completion status
                    // Show only if terminado = 1 AND estado = 1 (completed and active)
                    if (response.data.terminado == 1 && response.data.estado == 1) {
                        $('#edit_fecha_terminado_container').show();
                        // Make fields required when visible
                        $('#edit_fecha_terminado_date').prop('required', true);
                        $('#edit_fecha_terminado_time').prop('required', true);
                    } else {
                        $('#edit_fecha_terminado_container').hide();
                        // Remove required when hidden
                        $('#edit_fecha_terminado_date').prop('required', false);
                        $('#edit_fecha_terminado_time').prop('required', false);
                    }
                    
                    // Show the modal
                    $('#editHitModal').modal('show');
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error   : function () {
                alert('Error al obtener los datos del hit');
            }
        });
    }
    
    // Finish Hit Function
    function finishHit(hitId) {
        currentHitIdForFinish = hitId;
        $('#finishHitModal').modal('show');
    }
    
    // Return Hit Function
    function returnHit(hitId) {
        currentHitIdForReturn = hitId;
        $('#returnHitModal').modal('show');
    }
    
    // Confirm Finish Hit
    $('#confirmFinishHit').click(function () {
        if (currentHitIdForFinish) {
            // Disable the button to prevent multiple clicks
            const $button = $(this);
            $button.prop('disabled', true).text('Procesando...');
            
            $.ajax({
                url     : 'listado-hits',
                type    : 'POST',
                data    : {
                    ajax_finish_hit: true,
                    hit_id         : currentHitIdForFinish
                },
                dataType: 'json',
                success : function (response) {
                    if (response.success) {
                        $('#finishHitModal').modal('hide');
                        window.location.href = 'listado-hits'; // Redirect instead of reload to prevent form resubmission
                    } else {
                        alert('Error: ' + response.message);
                        // Re-enable button on error
                        $button.prop('disabled', false).text('Sí, Terminar');
                    }
                },
                error   : function () {
                    alert('Error al terminar el hit');
                    // Re-enable button on error
                    $button.prop('disabled', false).text('Sí, Terminar');
                }
            });
        }
    });
    
    // Confirm Return Hit
    $('#confirmReturnHit').click(function () {
        if (currentHitIdForReturn) {
            // Disable the button to prevent multiple clicks
            const $button = $(this);
            $button.prop('disabled', true).text('Procesando...');
            
            $.ajax({
                url     : 'listado-hits',
                type    : 'POST',
                data    : {
                    ajax_return_hit: true,
                    hit_id         : currentHitIdForReturn
                },
                dataType: 'json',
                success : function (response) {
                    if (response.success) {
                        $('#returnHitModal').modal('hide');
                        window.location.href = 'listado-hits'; // Redirect instead of reload to prevent form resubmission
                    } else {
                        alert('Error: ' + response.message);
                        // Re-enable button on error
                        $button.prop('disabled', false).text('Sí, Retornar');
                    }
                },
                error   : function () {
                    alert('Error al retornar el hit');
                    // Re-enable button on error
                    $button.prop('disabled', false).text('Sí, Retornar');
                }
            });
        }
    });
    
    // Copy TRK ID to clipboard function
    function copyTrkId() {
        const trkIdElement = document.getElementById('trk-id-display');
        const trkIdValue   = trkIdElement.textContent.trim();
        
        // Use the modern Clipboard API if available
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(trkIdValue).then(function () {
                showCopiedTooltip();
            }).catch(function (err) {
                // Fallback to older method
                fallbackCopyTextToClipboard(trkIdValue);
            });
        } else {
            // Fallback for older browsers
            fallbackCopyTextToClipboard(trkIdValue);
        }
    }
    
    // Fallback copy method for older browsers
    function fallbackCopyTextToClipboard(text) {
        const textArea          = document.createElement("textarea");
        textArea.value          = text;
        textArea.style.position = "fixed";
        textArea.style.left     = "-999999px";
        textArea.style.top      = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            showCopiedTooltip();
        } catch (err) {
            console.error('Unable to copy to clipboard', err);
        }
        
        document.body.removeChild(textArea);
    }
    
    // Show "Copiado" tooltip
    function showCopiedTooltip() {
        const trkIdElement = document.getElementById('trk-id-display');
        
        // Create tooltip element
        const tooltip         = document.createElement('div');
        tooltip.textContent   = 'Copiado';
        tooltip.style.cssText = `
            position: absolute;
            background-color: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        // Position tooltip above the element
        const rect         = trkIdElement.getBoundingClientRect();
        tooltip.style.left = (rect.left + rect.width / 2 - 25) + 'px';
        tooltip.style.top  = (rect.top - 30) + 'px';
        
        document.body.appendChild(tooltip);
        
        // Show tooltip with fade in
        setTimeout(() => {
            tooltip.style.opacity = '1';
        }, 10);
        
        // Hide tooltip after 2 seconds
        setTimeout(() => {
            tooltip.style.opacity = '0';
            setTimeout(() => {
                if (tooltip.parentNode) {
                    document.body.removeChild(tooltip);
                }
            }, 300);
        }, 2000);
    }
</script>

<script type="text/javascript">
    // Active Hit Panel Real-time Updates
    let activeHitData      = null;
    let activeHitStartTime = null;
    let updateInterval     = null;
    let refreshInterval    = null;
    
    // Function to fetch current active hit data
    function fetchActiveHitData() {
        $.ajax({
            url     : 'listado-hits',
            type    : 'POST',
            data    : {
                ajax_get_current_active_hit: true
            },
            dataType: 'json',
            success : function (response) {
                if (response.success && response.data) {
                    // Update active hit data
                    activeHitData      = response.data;
                    activeHitStartTime = new Date(response.data.fecha);
                    
                    // Update panel content
                    $('#activeHitDescription').text(response.data.descripcion);
                    $('#activeHitRequester').text(response.data.requester);
                    $('#activeHitPago').text(parseFloat(response.data.pago).toFixed(2));
                    
                    // Show panel if hidden
                    if ($('#activeHitPanel').is(':hidden')) {
                        $('#activeHitPanel').fadeIn(500);
                    }
                    
                    // Start real-time updates if not already running
                    if (!updateInterval) {
                        startRealTimeUpdates();
                    }
                } else {
                    // No active hit, hide panel and reset title
                    if ($('#activeHitPanel').is(':visible')) {
                        $('#activeHitPanel').fadeOut(500);
                    }
                    stopRealTimeUpdates();
                    // Reset title to default when no active hit
                    document.title = 'My Dash | Hits';
                }
            },
            error   : function () {
                console.error('Error fetching active hit data');
                // Hide panel on error and reset title
                if ($('#activeHitPanel').is(':visible')) {
                    $('#activeHitPanel').fadeOut(500);
                }
                stopRealTimeUpdates();
                // Reset title to default on error
                document.title = 'My Dash | Hits';
            }
        });
    }
    
    // Function to calculate and update duration
    function updateDuration() {
        if (!activeHitStartTime) return;
        
        const now         = new Date();
        const diffMs      = now - activeHitStartTime;
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        const diffSeconds = Math.floor((diffMs % (1000 * 60)) / 1000);
        
        // Format as MM:SS
        const formattedDuration = String(diffMinutes).padStart(2, '0') + ':' +
            String(diffSeconds).padStart(2, '0');
        
        $('#activeDuration').text(formattedDuration);
        
        // Update hourly rate
        updateHourlyRate(diffMinutes);
        
        // Update page title
        updatePageTitle(diffMinutes, formattedDuration);
    }
    
    // Function to calculate and update hourly rate
    function updateHourlyRate(elapsedMinutes) {
        if (!activeHitData || elapsedMinutes <= 0) {
            $('#activeHourlyRate').text('$0.00/hr');
            return;
        }
        
        const pago       = parseFloat(activeHitData.pago);
        const hourlyRate = (pago / elapsedMinutes) * 60;
        
        $('#activeHourlyRate').text('$' + hourlyRate.toFixed(2) + '/hr');
    }
    
    // Function to update page title based on active hit status
    function updatePageTitle(elapsedMinutes, formattedDuration) {
        if (!activeHitData) {
            // No active hit - use default title
            document.title = 'My Dash | Hits';
            return;
        }
        
        // Calculate hourly rate
        let hourlyRate = 0;
        if (elapsedMinutes > 0) {
            const pago = parseFloat(activeHitData.pago);
            hourlyRate = (pago / elapsedMinutes) * 60;
        }
        
        // Update title with hourly rate and active time
        const hourlyRateFormatted = '$' + hourlyRate.toFixed(2) + '/hr';
        const activeTimeFormatted = formattedDuration + ' activo';
        document.title            = 'My Dash | ' + hourlyRateFormatted + ' - ' + activeTimeFormatted;
    }
    
    // Function to start real-time updates
    function startRealTimeUpdates() {
        // Update duration and hourly rate every second
        updateInterval = setInterval(updateDuration, 1000);
    }
    
    // Function to stop real-time updates
    function stopRealTimeUpdates() {
        if (updateInterval) {
            clearInterval(updateInterval);
            updateInterval = null;
        }
    }
    
    // Initialize on page load
    $(document).ready(function () {
        // Initial fetch
        fetchActiveHitData();
        
        // Refresh active hit data every 30 seconds
        refreshInterval = setInterval(fetchActiveHitData, 30000);
        
        // Initial title will be set by fetchActiveHitData() which handles both cases:
        // - If there's an active hit, it will update the title with real-time data
        // - If there's no active hit, it will set the title to default 'My Dash | Hits'
    });
    
    // Clean up intervals when page is unloaded
    $(window).on('beforeunload', function () {
        stopRealTimeUpdates();
        if (refreshInterval) {
            clearInterval(refreshInterval);
        }
    });
    
    // Function to finish the currently active hit
    function finishActiveHit() {
        if (activeHitData && activeHitData.id) {
            // Call the existing finishHit function with the active hit ID
            finishHit(activeHitData.id);
        } else {
            alert('No hay hit activo para terminar');
        }
    }
    
    // Function to return the currently active hit
    function returnActiveHit() {
        if (activeHitData && activeHitData.id) {
            // Call the existing returnHit function with the active hit ID
            returnHit(activeHitData.id);
        } else {
            alert('No hay hit activo para retornar');
        }
    }
</script>
<?php #endregion js ?>

</body>
</html>
